<div class="timezone-handler">
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get user's timezone
            const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

            // Store timezone in session storage for later use
            sessionStorage.setItem('userTimezone', userTimezone);

            // Add timezone info to forms
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                // Check if timezone input already exists
                if (!form.querySelector('input[name="user_timezone"]')) {
                    const timezoneInput = document.createElement('input');
                    timezoneInput.type = 'hidden';
                    timezoneInput.name = 'user_timezone';
                    timezoneInput.value = userTimezone;
                    form.appendChild(timezoneInput);
                }
            });

            console.log('User timezone detected:', userTimezone);
        });
    </script>

    <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div>
                <p class="text-sm font-medium text-blue-800">Timezone Information</p>
                <p class="text-xs text-blue-600">Your local timezone will be detected automatically. Times will be
                    converted to UTC for storage.</p>
            </div>
        </div>
    </div>
</div>
