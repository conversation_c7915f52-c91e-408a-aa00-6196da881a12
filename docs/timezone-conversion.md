# Global Timezone Conversion Implementation

## Overview

This implementation provides **automatic timezone conversion for ALL datetime fields across ALL Filament panels**. The system ensures that:

1. Users can input times in their local timezone across all forms
2. Times are automatically converted to UTC for storage in the database
3. Times are displayed in user's local timezone in tables and views
4. **Zero configuration required** - works automatically for all datetime fields

## Components

### 1. Global Backend Components

#### `app/Concerns/HandlesTimezoneConversion.php`
A trait that provides timezone conversion utilities and **automatic datetime conversion**:
- `convertToUtc()` - Converts datetime from user timezone to UTC
- `convertFromUtc()` - Converts datetime from UTC to user timezone
- `getUserTimezone()` - Gets user timezone from request or session
- `setAttribute()` - **Automatically converts ALL datetime fields to UTC when saving**

#### `app/Providers/TimezoneServiceProvider.php`
Global service provider that:
- **Replaces all DateTimePicker components** with timezone-aware versions
- Adds timezone detection JavaScript to all Filament panels
- Registers `dateTimeWithTimezone()` macro for table columns
- Stores user timezone in session automatically

#### `app/Filament/Components/TimezoneAwareDateTimePicker.php`
Custom DateTimePicker that:
- Automatically converts input from user timezone to UTC
- Displays stored UTC values in user's local timezone
- **Works for ALL DateTimePicker fields across the application**

### 2. Global Frontend Components

#### JavaScript Timezone Detection
Automatically injected into all Filament panels:
- Detects user's browser timezone using `Intl.DateTimeFormat()`
- Stores timezone in session via API call
- Adds hidden timezone field to all forms
- **No manual configuration required**

### 3. Updated Models

All models with datetime fields now use the `HandlesTimezoneConversion` trait:
- `TripService` - departure_datetime, arrival_datetime, etc.
- `ShippingService` - departure_datetime, arrival_datetime, etc.
- `FazaaService` - departure_datetime, arrival_datetime, etc.
- `TripRequest` - departure_datetime, arrival_datetime, etc.
- **Any model with datetime casts gets automatic conversion**

## How It Works

### Global Automatic Process

1. **Page Load**: JavaScript automatically detects user's timezone (e.g., "Asia/Riyadh")
2. **Session Storage**: Timezone is stored in session via API call
3. **Form Input**: User enters any datetime in any form (e.g., "2024-01-15 20:00")
4. **Automatic Conversion**: `HandlesTimezoneConversion` trait converts to UTC before saving
5. **Database Storage**: All datetimes stored in UTC (e.g., "2024-01-15 17:00")
6. **Display**: Tables show times converted back to user's timezone

### Example Flow

**Creating ANY Service with Datetime Fields:**
1. User in Saudi Arabia (UTC+3) opens any form
2. User enters "8:00 PM" local time
3. System automatically converts to "5:00 PM UTC"
4. Database stores "17:00:00 UTC"
5. Mobile app receives "17:00:00 UTC" from API
6. Tables display "8:00 PM" (converted back to user's timezone)

**Works for ALL datetime fields:**
- Trip services: departure_datetime, arrival_datetime
- Shipping services: departure_datetime, arrival_datetime
- Fazaa services: departure_datetime, arrival_datetime
- Bookings: created_at, confirmed_at, cancelled_at
- **Any model with datetime casts**

## Configuration

### Language Files

Added translation keys in `lang/en/messages.php` and `lang/ar/messages.php`:
```php
'time_will_be_converted_to_utc' => 'Time will be automatically converted from your local timezone to UTC for storage'
```

### Application Timezone

The application timezone is set to UTC in `config/app.php`:
```php
'timezone' => env('APP_TIMEZONE', 'UTC'),
```

## Testing

To test the timezone conversion:

1. Create a new trip service with a specific time
2. Check the database to verify the time is stored in UTC
3. View the trip in the admin interface to see UTC display
4. Test from different timezones to verify conversion

## Benefits

1. **Global Consistency**: All times stored in UTC regardless of user location
2. **User Friendly**: Users can input times in their local timezone
3. **Clear Communication**: UI clearly indicates when times are in UTC
4. **Automatic**: No manual timezone selection required
5. **Robust**: Handles timezone detection and conversion errors gracefully

## Future Enhancements

1. Add user timezone preferences to user profiles
2. Display times in user's preferred timezone in tables
3. Add timezone conversion for other datetime fields
4. Implement timezone-aware filtering and searching
