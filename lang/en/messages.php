<?php

return [
    'response' => [
        'get_data' => 'Resource fetched successfully.',
        'create_data' => 'Resource created successfully.',
        'update_data' => 'Resource updated successfully.',
        'delete_data' => 'Resource deleted successfully.',
    ],
    'auth' => [],
    'validation' => [
        'trip_service' => [
            'departure_datetime_not_earlier' => 'The new departure time cannot be earlier than the current departure time.',
            'seats_count_exceeds_maximum' => 'The number of seats you selected exceeds the maximum allowed limit of :max. Please select a smaller number of seats.',
        ],
    ],
    'trip_service' => [
        'bookings' => [
            'accepted' => 'Booking accepted successfully.',
            'rejected' => 'Booking rejected successfully.',
            'late_arrival_notified_successfully' => 'Trip service provider has been notified about your late arrival.',
            'auto_rejected' => 'Your trip booking has been automatically cancelled.',
            'payment_reminders_sent' => 'Payment reminders sent successfully.',
        ],
        'attendance_confirmed' => 'Trip service attendance has been confirmed successfully.',
        'delayed_successfully' => 'Trip service delayed successfully.',
        'arrival_confirmed' => 'Trip service arrival has been confirmed successfully.',
    ],
    'trip_request' => [
        'attendance_confirmed' => 'Attendance confirmed successfully',
        'delayed_successfully' => 'Trip request delayed successfully',
        'bookings' => [
            'auto_rejected' => 'Your trip request has been automatically cancelled.',
        ],
    ],
    'shipping_service' => [
        'delayed_successfully' => 'Shipping service delayed successfully.',
        'bookings' => [
            'late_arrival_notified_successfully' => 'Shipping service provider has been notified about your late arrival.',
            'auto_rejected' => 'Your shipping request has been automatically cancelled.',
        ],
    ],
    'fazaa_service' => [
        'delayed_successfully' => 'Fazaa service delayed successfully.',
        'bookings' => [
            'late_arrival_notified_successfully' => 'Fazaa service provider has been notified about your late arrival.',
            'auto_rejected' => 'Your fazaa request has been automatically cancelled.',
        ],
    ],
    'shipping_request' => [
        'delayed_successfully' => 'Shipping request delayed successfully.',
        'bookings' => [
            'auto_rejected' => 'Your shipping request has been automatically cancelled.',
        ],
    ],
    'trip_request' => [
        'delayed_successfully' => 'Trip request delayed successfully.',
        'bookings' => [
            'auto_rejected' => 'Your trip request has been automatically cancelled.',
        ],
    ],
    'fazaa_request' => [
        'delayed_successfully' => 'Fazaa request delayed successfully.',
        'bookings' => [
            'auto_rejected' => 'Your fazaa request has been automatically cancelled.',
        ],
    ],
    'shipping_request' => [
        'delayed_successfully' => 'Shipping request delayed successfully.',
        'bookings' => [
            'auto_rejected' => 'Your shipping request has been automatically cancelled.',
        ],
    ],

    // Transport Messages
    'transport' => [
        'welcome_title' => 'Welcome to :office_name',
        'welcome_subtitle' => 'Hello :user_name, manage your transportation services from here.',
        'no_office_info' => 'No office information available.',
        'not_provided' => 'Not provided',
        'settings_saved_title' => 'Settings Saved',
        'settings_saved_body' => 'Your office settings have been updated successfully.',
        'registration_success' => 'Transportation office registered successfully!',
        'registration_success_body' => 'Your office has been created and you can now start managing your transportation services.',

        // Helper texts
        'password_requirements' => 'Password must be at least 8 characters long',
        'status_readonly' => 'Status can only be changed by administrators',
        'logo_requirements' => 'Upload your office logo (max 2MB, JPG/PNG)',
        'license_document_requirements' => 'Upload your transportation license (max 5MB, PDF/Image)',
        'registration_documents_requirements' => 'Upload registration document (max 5MB, PDF/Image)',
        'select_driver_from_office' => 'Select a driver from your office drivers list',
        'select_car_for_driver' => 'Select a car belonging to the selected driver',
        'seat_management_info' => 'Seats A1 and A2 are reserved for driver and front passenger',
        'seats_a1_a2_unavailable' => 'Note: Seats A1 and A2 are automatically reserved and not available for booking',
        'select_car_first' => 'Please select a car first to see seat layout',
        'car_not_found' => 'Selected car not found',
        'seat_layout_info' => 'Car: :car_type | Total Seats: :total | Available for Booking: :available',
        'select_seats_help' => 'Select which seats customers can book. A1 (driver) and A2 (front passenger) are always reserved.',
        'reserved_seats_layout' => '🚗 A1 (Driver) | 👤 A2 (Front Passenger) - Always Reserved',

        // Seat selector component
        'seat_available' => 'Available',
        'seat_reserved' => 'Reserved',
        'seat_selected' => 'Selected',
        'selected_seats' => 'Selected Seats',
        'total_selected_seats' => 'Total: ',

        // Seat selector messages
        'seat_available' => 'Available for Booking',
        'seat_reserved' => 'Reserved',
        'seat_selected' => 'Selected',
        'selected_seats' => 'Selected Seats',
        'total_selected_seats' => 'Total Selected Seats: :count',

        // Statistics
        'office_status' => 'Office Status',
        'license_status' => 'License Status',
        'licensed' => 'Licensed',
        'unlicensed' => 'Unlicensed',
        'established' => 'Established',

        // Status messages
        'status' => [
            'active' => 'Active',
            'inactive' => 'Inactive',
            'suspended' => 'Suspended',
        ],

        // Authentication messages
        'auth' => [
            'access_denied' => 'Access Denied',
            'account_inactive' => 'Your office account is inactive. Please contact administrator for activation.',
            'account_suspended' => 'Your office account has been suspended. Please contact administrator.',
            'account_not_accessible' => 'Your office account is not accessible.',
        ],

        // Timezone messages
        'time_will_be_converted_to_utc' => 'Time will be automatically converted from your local timezone to UTC for storage',
    ],
];
