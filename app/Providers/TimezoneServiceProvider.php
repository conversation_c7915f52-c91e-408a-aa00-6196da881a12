<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Filament\Support\Facades\FilamentView;
use Illuminate\Support\Facades\Blade;
use Filament\Forms\Components\DateTimePicker;
use Filament\Tables\Columns\TextColumn;

class TimezoneServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        //
    }

    public function boot(): void
    {
        // Global configuration for DateTimePicker - automatically handles timezone
        DateTimePicker::configureUsing(function (DateTimePicker $component): void {
            $userTimezone = $this->getUserTimezone();
            $component->timezone($userTimezone);
        });

        // Global configuration for TextColumn - automatically handles datetime fields
        TextColumn::configureUsing(function (TextColumn $component): void {
            $userTimezone = $this->getUserTimezone();

            // Apply timezone to common datetime fields
            if (in_array($component->getName(), [
                'created_at',
                'updated_at',
                'deleted_at',
                'email_verified_at',
                'departure_datetime',
                'arrival_datetime',
                'confirmed_at',
                'cancelled_at',
                'delayed_at',
                'read_at',
                'paid_at',
                'attendance_confirmed_at',
                'arrival_confirmed_at'
            ])) {
                $component->timezone($userTimezone);
            }
        });

        // Add timezone detection script to all Filament panels
        FilamentView::registerRenderHook(
            'panels::body.end',
            fn(): string => Blade::render('
                <script>
                    document.addEventListener("DOMContentLoaded", function() {
                        // Get user timezone
                        const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

                        // Store timezone in session for server-side access
                        fetch("/api/set-timezone", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                "X-CSRF-TOKEN": document.querySelector("meta[name=csrf-token]")?.getAttribute("content") || ""
                            },
                            body: JSON.stringify({ timezone: userTimezone })
                        }).catch(() => {
                            // Silently fail if endpoint doesn\'t exist
                        });
                    });
                </script>
            ')
        );
    }

    /**
     * Get user timezone with fallbacks
     */
    private function getUserTimezone(): string
    {
        // Try to get from authenticated user first
        $user = auth()->user();
        if ($user && $user->timezone) {
            return $user->timezone;
        }

        // Try to get from session (set by JavaScript)
        if (session()->has('user_timezone')) {
            return session('user_timezone');
        }

        // Default to application timezone
        return config('app.timezone', 'UTC');
    }
}
