<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Filament\Support\Facades\FilamentView;
use Illuminate\Support\Facades\Blade;
use Filament\Forms\Components\DateTimePicker;
use Filament\Tables\Columns\TextColumn;
use App\Filament\Components\TimezoneAwareDateTimePicker;
use App\Filament\Components\TimezoneAwareTextColumn;

class TimezoneServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        // Override default Filament components with timezone-aware versions
        $this->app->bind(DateTimePicker::class, TimezoneAwareDateTimePicker::class);
    }

    public function boot(): void
    {
        // Add timezone detection script to Filament panels
        FilamentView::registerRenderHook(
            'panels::body.end',
            fn(): string => Blade::render('
                <script>
                    document.addEventListener("DOMContentLoaded", function() {
                        // Get user timezone and store it
                        const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
                        sessionStorage.setItem("userTimezone", userTimezone);

                        // Store timezone in session for server-side access
                        fetch("/api/set-timezone", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                "X-CSRF-TOKEN": document.querySelector("meta[name=csrf-token]")?.getAttribute("content") || ""
                            },
                            body: JSON.stringify({ timezone: userTimezone })
                        }).catch(() => {
                            // Silently fail if endpoint doesn\'t exist
                        });

                        // Add timezone to forms silently
                        const forms = document.querySelectorAll("form");
                        forms.forEach(form => {
                            if (!form.querySelector("input[name=\"user_timezone\"]")) {
                                const timezoneInput = document.createElement("input");
                                timezoneInput.type = "hidden";
                                timezoneInput.name = "user_timezone";
                                timezoneInput.value = userTimezone;
                                form.appendChild(timezoneInput);
                            }
                        });
                    });
                </script>
            ')
        );

        // Register macro for timezone-aware datetime columns
        TextColumn::macro('dateTimeWithTimezone', function (string $format = 'Y-m-d H:i') {
            /** @var TextColumn $this */
            return $this->formatStateUsing(function ($state) use ($format) {
                if (!$state) {
                    return null;
                }

                // Get user timezone from session or default to UTC
                $userTimezone = session('user_timezone', 'UTC');

                try {
                    // Parse the UTC datetime and convert to user's timezone
                    return \Carbon\Carbon::parse($state, 'UTC')
                        ->setTimezone($userTimezone)
                        ->format($format);
                } catch (\Exception $e) {
                    // Fallback: return as-is
                    return $state;
                }
            });
        });
    }
}
