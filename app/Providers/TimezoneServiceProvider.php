<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Filament\Support\Facades\FilamentView;
use Illuminate\Support\Facades\Blade;

class TimezoneServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        //
    }

    public function boot(): void
    {
        // Add timezone detection script to Filament panels
        FilamentView::registerRenderHook(
            'panels::body.end',
            fn (): string => Blade::render('
                <script>
                    document.addEventListener("DOMContentLoaded", function() {
                        // Get user timezone and store it
                        const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
                        sessionStorage.setItem("userTimezone", userTimezone);
                        
                        // Add timezone to forms silently
                        const forms = document.querySelectorAll("form");
                        forms.forEach(form => {
                            if (!form.querySelector("input[name=\"user_timezone\"]")) {
                                const timezoneInput = document.createElement("input");
                                timezoneInput.type = "hidden";
                                timezoneInput.name = "user_timezone";
                                timezoneInput.value = userTimezone;
                                form.appendChild(timezoneInput);
                            }
                        });
                    });
                </script>
            ')
        );
    }
}
