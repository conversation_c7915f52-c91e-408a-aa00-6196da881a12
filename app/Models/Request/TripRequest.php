<?php

namespace App\Models\Request;

use App\Enums\Travel\TripStatusEnum;
use App\Models\Booking\TripRequestBooking;
use App\Models\User;
use Snowflake\Snowflakes;
use App\Models\Common\City;
use Snowflake\SnowflakeCast;
use Illuminate\Support\Carbon;
use App\Models\Common\CarType;
use App\Models\Service\AdditionalService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Database\Factories\Request\TripRequestFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Dashboard\Admin;
use App\Traits\HasReadTracking;
use App\Concerns\HandlesTimezoneConversion;

class TripRequest extends Model
{
    use HasFactory, Snowflakes, HasReadTracking, HandlesTimezoneConversion;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'trip_type',
        'departure_datetime',
        'arrival_datetime',
        'number_of_seats',
        'from_city_id',
        'to_city_id',
        'from_location_lat',
        'from_location_lng',
        'to_location_lat',
        'to_location_lng',
        'price',
        'user_id',
        'allow_smoking',
        'deliver_to_door',
        'car_type_id',
        'arrive_destination',
        'note',
        'cancelled_at',
        'cancellation_reason',
        'cancellation_note',
        'attendance_confirmed_at',
        'delayed_at',
        'delay_reason',
        'delay_note',
        'read_at',
        'read_by_id',
        'weather_status',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => SnowflakeCast::class,
            'departure_datetime' => 'datetime',
            'arrival_datetime' => 'datetime',
            'price' => 'float',
            'allow_smoking' => 'boolean',
            'deliver_to_door' => 'boolean',
            'user_id' => SnowflakeCast::class,
            'from_city_id' => SnowflakeCast::class,
            'to_city_id' => SnowflakeCast::class,
            'to_location_lat' => 'float',
            'to_location_lng' => 'float',
            'from_location_lat' => 'float',
            'from_location_lng' => 'float',
            'attendance_confirmed_at' => 'datetime',
            'delayed_at' => 'datetime',
            'cancelled_at' => 'datetime',
            'read_at' => 'datetime',
            'read_by_id' => SnowflakeCast::class,
        ];
    }

    /**
     * Get the user that owns the trip request.
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the departure city of the trip request.
     *
     * @return BelongsTo
     */
    public function fromCity(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }

    /**
     * Get the arrival city of the trip request.
     *
     * @return BelongsTo
     */
    public function toCity(): BelongsTo
    {
        return $this->belongsTo(City::class, 'to_city_id');
    }

    /**
     * Get the seats associated with the trip request.
     *
     * @return HasMany
     */
    public function seats(): HasMany
    {
        return $this->hasMany(TripRequestSeat::class);
    }

    /**
     * Get the car type associated with the trip request.
     *
     * @return BelongsTo
     */
    public function carType(): BelongsTo
    {
        return $this->belongsTo(CarType::class);
    }

    /**
     * Get the additional services for the trip request.
     *
     * @return BelongsToMany
     */
    public function additionalServices(): BelongsToMany
    {
        return $this->belongsToMany(AdditionalService::class, 'additional_service_trip_request')->using(AdditionalTripService::class);
    }

    /**
     * Get the bookings for the trip request.
     *
     * @return HasMany
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(TripRequestBooking::class);
    }

    /**
     * Create a new factory instance for the model.
     *
     * @return TripRequestFactory
     */
    protected static function newFactory(): TripRequestFactory
    {
        return TripRequestFactory::new();
    }

    /**
     * Scope a query to only include available trips.
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeAvailable(Builder $query): Builder
    {
        return $query->whereDoesntHave('bookings');
    }

    /**
     * Scope a query to only include upcoming trips.
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeUpcoming(Builder $query): Builder
    {
        return $query->where('departure_datetime', '>', Carbon::now());
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeActive(Builder $query)
    {
        return $query->where('arrival_datetime', '>=', Carbon::now())->whereNull('cancelled_at');
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeCompleted(Builder $query)
    {
        return $query->where('arrival_datetime', '<', Carbon::now());
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeCancelled(Builder $query)
    {
        return $query->whereNotNull('cancelled_at');
    }

    /**
     * Get the formatted trip type.
     *
     * @return Attribute
     */
    protected function tripTypeFormatted(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value, array $attributes) => __('enums.trip_type.' . $attributes['trip_type']),
        );
    }

    /**
     * @return Attribute
     */
    protected function status(): Attribute
    {
        return Attribute::make(
            get: function (mixed $value, array $attributes) {
                return match (true) {
                    $attributes['cancelled_at'] !== null => TripStatusEnum::CANCELLED(),
                    $attributes['arrival_datetime'] <= Carbon::now() => TripStatusEnum::COMPLETED(),
                    $attributes['arrival_datetime'] > Carbon::now() => TripStatusEnum::ACTIVE(),
                    default => TripStatusEnum::ACTIVE(),
                };
            }
        );
    }
}
