<?php

namespace App\Filament\Transport\Resources;

use App\Enums\Travel\SeatStatusEnum;
use App\Enums\Travel\TripTypeEnum;
use App\Filament\Transport\Resources\TripServiceResource\Pages;
use App\Models\Common\Car;
use App\Models\Common\City;
use App\Models\Service\TripService;
use App\Models\TransportationOfficeDriver;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Cheesegrits\FilamentGoogleMaps\Fields\Map;

class TripServiceResource extends Resource
{
    protected static ?string $model = TripService::class;

    protected static ?string $navigationIcon = 'heroicon-o-truck';

    protected static ?string $navigationGroup = null;

    public static function getNavigationGroup(): ?string
    {
        return __('navigation.transport.trip_management');
    }

    public static function getModelLabel(): string
    {
        return __('models.transport.trip_service');
    }

    public static function getPluralModelLabel(): string
    {
        return __('models.transport.trip_services');
    }

    public static function getNavigationLabel(): string
    {
        return __('navigation.transport.trips');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('sections.transport.trip_information'))
                    ->schema([
                        Forms\Components\Select::make('trip_type')
                            ->label(__('fields.transport.trip_type'))
                            ->options([
                                TripTypeEnum::FAMILIES->value => __('enums.trip_type.' . TripTypeEnum::FAMILIES->value),
                                TripTypeEnum::SINGLES->value => __('enums.trip_type.' . TripTypeEnum::SINGLES->value),
                            ])
                            ->required()
                            ->default(TripTypeEnum::FAMILIES->value),

                        Forms\Components\Select::make('user_id')
                            ->label(__('fields.transport.driver'))
                            ->options(function () {
                                $currentOfficeId = Auth::guard('transport')->user()?->transportation_office_id;

                                return TransportationOfficeDriver::where('transportation_office_id', $currentOfficeId)
                                    ->where('status', 'active')
                                    ->with('user')
                                    ->get()
                                    ->pluck('user.name', 'user_id');
                            })
                            ->searchable()
                            ->required()
                            ->helperText(__('messages.transport.select_driver_from_office')),

                        Forms\Components\Select::make('car_id')
                            ->label(__('fields.transport.car'))
                            ->options(function (Forms\Get $get) {
                                if (!$get('user_id')) {
                                    return [];
                                }

                                return Car::where('user_id', $get('user_id'))
                                    ->with('type')
                                    ->get()
                                    ->mapWithKeys(function ($car) {
                                        return [$car->id => $car->type->name . ' - ' . $car->model . ' (' . $car->plate_number . ')'];
                                    });
                            })
                            ->searchable()
                            ->required()
                            ->live()
                            ->helperText(__('messages.transport.select_car_for_driver')),
                    ])
                    ->columns(2),

                Forms\Components\Section::make(__('sections.transport.route_information'))
                    ->schema([
                        Forms\Components\Select::make('from_city_id')
                            ->label(__('fields.transport.from_city'))
                            ->options(function () {
                                return City::all()->mapWithKeys(function ($city) {
                                    return [$city->id => $city->name];
                                });
                            })
                            ->searchable()
                            ->required(),

                        Forms\Components\Select::make('to_city_id')
                            ->label(__('fields.transport.to_city'))
                            ->options(function () {
                                return City::all()->mapWithKeys(function ($city) {
                                    return [$city->id => $city->name];
                                });
                            })
                            ->searchable()
                            ->required(),

                        // From Location Map Picker
                        Map::make('from_location')
                            ->label(__('fields.transport.from_location'))
                            ->columnSpanFull()
                            ->defaultLocation([24.7136, 46.6753]) // Riyadh, Saudi Arabia
                            ->mapControls([
                                'mapTypeControl' => true,
                                'scaleControl' => true,
                                'streetViewControl' => true,
                                'rotateControl' => true,
                                'fullscreenControl' => true,
                                'searchBoxControl' => false,
                                'zoomControl' => true,
                            ])
                            ->height('400px')
                            ->defaultZoom(10)
                            ->draggable()
                            ->clickable()
                            ->type('roadmap'),

                        // To Location Map Picker
                        Map::make('to_location')
                            ->label(__('fields.transport.to_location'))
                            ->columnSpanFull()
                            ->defaultLocation([21.4225, 39.8262]) // Jeddah, Saudi Arabia
                            ->mapControls([
                                'mapTypeControl' => true,
                                'scaleControl' => true,
                                'streetViewControl' => true,
                                'rotateControl' => true,
                                'fullscreenControl' => true,
                                'searchBoxControl' => false,
                                'zoomControl' => true,
                            ])
                            ->height('400px')
                            ->defaultZoom(10)
                            ->draggable()
                            ->clickable()
                            ->type('roadmap'),
                    ])
                    ->columns(1),

                Forms\Components\Section::make(__('sections.transport.schedule_information'))
                    ->schema([
                        Forms\Components\DateTimePicker::make('departure_datetime')
                            ->label(__('fields.transport.departure_datetime'))
                            ->required()
                            ->minDate(now()->addMinutes(5)->startOfMinute())
                            ->seconds(false)
                            ->minutesStep(5)
                            ->native(false),

                        Forms\Components\DateTimePicker::make('arrival_datetime')
                            ->label(__('fields.transport.arrival_datetime'))
                            ->required()
                            ->minDate(now()->addMinutes(5)->startOfMinute())
                            ->seconds(false)
                            ->minutesStep(5)
                            ->native(false)
                            ->after('departure_datetime'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make(__('sections.transport.pricing_and_options'))
                    ->schema([
                        Forms\Components\TextInput::make('price')
                            ->label(__('fields.transport.price'))
                            ->numeric()
                            ->required()
                            ->prefix('SAR')
                            ->step(0.01)
                            ->minValue(0),

                        Forms\Components\TextInput::make('number_of_free_cartons')
                            ->label(__('fields.transport.free_cartons'))
                            ->numeric()
                            ->default(0)
                            ->minValue(0),

                        Forms\Components\Toggle::make('allow_smoking')
                            ->label(__('fields.transport.allow_smoking'))
                            ->default(false),

                        Forms\Components\Toggle::make('deliver_to_door')
                            ->label(__('fields.transport.deliver_to_door'))
                            ->default(false),

                        Forms\Components\Textarea::make('note')
                            ->label(__('fields.transport.note'))
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make(__('sections.transport.seat_management'))
                    ->schema([
                        Forms\Components\Placeholder::make('seat_layout_info')
                            ->label(__('fields.transport.seat_layout'))
                            ->content(function (Forms\Get $get) {
                                $carId = $get('car_id');
                                if (!$carId) {
                                    return __('messages.transport.select_car_first');
                                }

                                $car = Car::with('type')->find($carId);
                                if (!$car) {
                                    return __('messages.transport.car_not_found');
                                }

                                $totalSeats = $car->type->number_of_seats;
                                $availableSeats = $totalSeats + 2;

                                return __('messages.transport.seat_layout_info', [
                                    'total' => $totalSeats,
                                    'available' => $availableSeats,
                                    'car_type' => $car->type->name
                                ]);
                            }),

                        \App\Filament\Transport\Components\SeatSelector::make('available_seats')
                            ->label(__('fields.transport.select_available_seats'))
                            ->required(fn(Forms\Get $get) => $get('car_id'))
                            ->live()
                            ->visible(fn(Forms\Get $get) => $get('car_id'))
                            ->helperText(__('messages.transport.select_seats_help')),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('fromCity.name')
                    ->label(__('fields.transport.from_city'))
                    ->getStateUsing(fn($record) => $record->fromCity?->name)
                    ->searchable(['fromCity.name_ar', 'fromCity.name_en'])
                    ->sortable(),

                Tables\Columns\TextColumn::make('toCity.name')
                    ->label(__('fields.transport.to_city'))
                    ->getStateUsing(fn($record) => $record->toCity?->name)
                    ->searchable(['toCity.name_ar', 'toCity.name_en'])
                    ->sortable(),

                Tables\Columns\TextColumn::make('departure_datetime')
                    ->label(__('fields.transport.departure_datetime'))
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('arrival_datetime')
                    ->label(__('fields.transport.arrival_datetime'))
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('user.name')
                    ->label(__('fields.transport.driver'))
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('price')
                    ->label(__('fields.transport.price'))
                    ->money('SAR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('number_of_available_seats')
                    ->label(__('fields.transport.available_seats'))
                    ->badge()
                    ->color(fn(int $state): string => match (true) {
                        $state === 0 => 'danger',
                        $state <= 2 => 'warning',
                        default => 'success'
                    }),

                Tables\Columns\TextColumn::make('trip_type')
                    ->label(__('fields.transport.trip_type'))
                    ->badge()
                    ->formatStateUsing(fn(string $state): string => __('enums.trip_type.' . $state)),

                Tables\Columns\TextColumn::make('status')
                    ->label(__('fields.transport.status'))
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'active' => 'success',
                        'completed' => 'info',
                        'cancelled' => 'danger',
                        default => 'gray'
                    })
                    ->formatStateUsing(fn(string $state): string => __('enums.status.' . $state)),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('fields.transport.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('trip_type')
                    ->label(__('fields.transport.trip_type'))
                    ->options([
                        TripTypeEnum::FAMILIES->value => __('enums.trip_type.' . TripTypeEnum::FAMILIES->value),
                        TripTypeEnum::SINGLES->value => __('enums.trip_type.' . TripTypeEnum::SINGLES->value),
                    ]),

                Tables\Filters\SelectFilter::make('from_city_id')
                    ->label(__('fields.transport.from_city'))
                    ->options(function () {
                        return City::all()->mapWithKeys(function ($city) {
                            return [$city->id => $city->name];
                        });
                    }),

                Tables\Filters\SelectFilter::make('to_city_id')
                    ->label(__('fields.transport.to_city'))
                    ->options(function () {
                        return City::all()->mapWithKeys(function ($city) {
                            return [$city->id => $city->name];
                        });
                    }),

                Tables\Filters\Filter::make('upcoming')
                    ->label(__('filters.transport.upcoming_trips'))
                    ->query(fn(Builder $query): Builder => $query->upcoming()),

                Tables\Filters\Filter::make('available')
                    ->label(__('filters.transport.available_trips'))
                    ->query(fn(Builder $query): Builder => $query->available()),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('departure_datetime', 'desc');
    }

    public static function getEloquentQuery(): Builder
    {
        $currentOfficeId = Auth::guard('transport')->user()?->transportation_office_id;

        return parent::getEloquentQuery()
            ->where('transportation_office_id', $currentOfficeId)
            ->with(['fromCity', 'toCity', 'user', 'car.type', 'seats', 'transportationOffice']);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTripServices::route('/'),
            'create' => Pages\CreateTripService::route('/create'),
            'view' => Pages\ViewTripService::route('/{record}'),
            'edit' => Pages\EditTripService::route('/{record}/edit'),
        ];
    }
}
