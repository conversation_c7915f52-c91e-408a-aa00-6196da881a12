<?php

namespace App\Filament\Components;

use Filament\Forms\Components\DateTimePicker;
use Illuminate\Support\Carbon;

class TimezoneAwareDateTimePicker extends DateTimePicker
{
    protected function setUp(): void
    {
        parent::setUp();

        // Convert from user timezone to UTC when saving
        $this->dehydrateStateUsing(function ($state) {
            if (!$state) {
                return null;
            }

            // Get user timezone from request or default to UTC
            $userTimezone = $this->getUserTimezone();
            
            try {
                // Parse the datetime in user's timezone and convert to UTC
                return Carbon::createFromFormat('Y-m-d H:i:s', $state, $userTimezone)
                    ->utc()
                    ->format('Y-m-d H:i:s');
            } catch (\Exception $e) {
                // Fallback: try to parse as-is and convert to UTC
                try {
                    return Carbon::parse($state, $userTimezone)->utc()->format('Y-m-d H:i:s');
                } catch (\Exception $e) {
                    // Last resort: return as-is
                    return $state;
                }
            }
        });

        // Convert from UTC to user timezone when displaying
        $this->formatStateUsing(function ($state) {
            if (!$state) {
                return null;
            }

            // Get user timezone from request or default to UTC
            $userTimezone = $this->getUserTimezone();
            
            try {
                // Parse the UTC datetime and convert to user's timezone
                return Carbon::createFromFormat('Y-m-d H:i:s', $state, 'UTC')
                    ->setTimezone($userTimezone)
                    ->format('Y-m-d H:i:s');
            } catch (\Exception $e) {
                // Fallback: try to parse as-is and convert to user timezone
                try {
                    return Carbon::parse($state, 'UTC')->setTimezone($userTimezone)->format('Y-m-d H:i:s');
                } catch (\Exception $e) {
                    // Last resort: return as-is
                    return $state;
                }
            }
        });
    }

    protected function getUserTimezone(): string
    {
        // Try to get from request first
        if (request()->has('user_timezone')) {
            return request()->input('user_timezone');
        }

        // Try to get from session
        if (session()->has('user_timezone')) {
            return session('user_timezone');
        }

        // Default to UTC
        return 'UTC';
    }

    public static function make(string $name): static
    {
        $static = app(static::class, ['name' => $name]);
        $static->configure();

        return $static;
    }
}
