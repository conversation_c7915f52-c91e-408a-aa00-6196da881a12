<?php

namespace App\Filament\Components;

use Filament\Tables\Columns\TextColumn;
use Illuminate\Support\Carbon;

class TimezoneAwareTextColumn extends TextColumn
{
    protected function setUp(): void
    {
        parent::setUp();

        // Only apply timezone conversion for datetime columns
        $this->formatStateUsing(function ($state) {
            if (!$state) {
                return null;
            }

            // Check if this looks like a datetime
            if (!$this->isDateTime($state)) {
                return $state;
            }

            // Get user timezone from request or default to UTC
            $userTimezone = $this->getUserTimezone();
            
            try {
                // Parse the UTC datetime and convert to user's timezone
                return Carbon::createFromFormat('Y-m-d H:i:s', $state, 'UTC')
                    ->setTimezone($userTimezone)
                    ->format('Y-m-d H:i');
            } catch (\Exception $e) {
                // Fallback: try to parse as-is and convert to user timezone
                try {
                    return Carbon::parse($state, 'UTC')->setTimezone($userTimezone)->format('Y-m-d H:i');
                } catch (\Exception $e) {
                    // Last resort: return as-is
                    return $state;
                }
            }
        });
    }

    protected function isDateTime($value): bool
    {
        if (!is_string($value)) {
            return false;
        }

        // Check if it matches common datetime patterns
        return preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $value) ||
               preg_match('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/', $value);
    }

    protected function getUserTimezone(): string
    {
        // Try to get from request first
        if (request()->has('user_timezone')) {
            return request()->input('user_timezone');
        }

        // Try to get from session
        if (session()->has('user_timezone')) {
            return session('user_timezone');
        }

        // Default to UTC
        return 'UTC';
    }

    public static function make(string $name): static
    {
        $static = app(static::class, ['name' => $name]);
        $static->configure();

        return $static;
    }
}
