<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class TimezoneController extends Controller
{
    /**
     * Store user timezone in session and user model
     */
    public function setTimezone(Request $request): JsonResponse
    {
        $request->validate([
            'timezone' => 'required|string|max:50'
        ]);

        $timezone = $request->input('timezone');

        // Store timezone in session
        session(['user_timezone' => $timezone]);

        // If user is authenticated, also save to user model
        if (Auth::check()) {
            Auth::user()->update(['timezone' => $timezone]);
        }

        return response()->json(['success' => true]);
    }
}
