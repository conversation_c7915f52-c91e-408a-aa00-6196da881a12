<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class TimezoneController extends Controller
{
    /**
     * Store user timezone in session
     */
    public function setTimezone(Request $request): JsonResponse
    {
        $request->validate([
            'timezone' => 'required|string|max:50'
        ]);

        // Store timezone in session
        session(['user_timezone' => $request->input('timezone')]);

        return response()->json(['success' => true]);
    }
}
