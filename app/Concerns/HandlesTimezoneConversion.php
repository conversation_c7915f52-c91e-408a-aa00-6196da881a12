<?php

namespace App\Concerns;

use Illuminate\Support\Carbon;

trait HandlesTimezoneConversion
{
    /**
     * Convert datetime from user timezone to UTC
     *
     * @param string|null $datetime
     * @param string|null $userTimezone
     * @return string|null
     */
    protected function convertToUtc(?string $datetime, ?string $userTimezone = null): ?string
    {
        if (!$datetime) {
            return null;
        }

        // Default to UTC if no user timezone provided
        $userTimezone = $userTimezone ?: 'UTC';

        try {
            // Parse the datetime assuming it's in the user's timezone
            $carbon = Carbon::createFromFormat('Y-m-d H:i:s', $datetime, $userTimezone);
            
            // Convert to UTC
            return $carbon->utc()->format('Y-m-d H:i:s');
        } catch (\Exception $e) {
            // Fallback: try to parse as-is and convert to UTC
            try {
                return Carbon::parse($datetime, $userTimezone)->utc()->format('Y-m-d H:i:s');
            } catch (\Exception $e) {
                // Last resort: return as-is
                return $datetime;
            }
        }
    }

    /**
     * Convert datetime from UTC to user timezone
     *
     * @param string|null $datetime
     * @param string|null $userTimezone
     * @return string|null
     */
    protected function convertFromUtc(?string $datetime, ?string $userTimezone = null): ?string
    {
        if (!$datetime) {
            return null;
        }

        // Default to UTC if no user timezone provided
        $userTimezone = $userTimezone ?: 'UTC';

        try {
            // Parse the datetime as UTC
            $carbon = Carbon::createFromFormat('Y-m-d H:i:s', $datetime, 'UTC');
            
            // Convert to user timezone
            return $carbon->setTimezone($userTimezone)->format('Y-m-d H:i:s');
        } catch (\Exception $e) {
            // Fallback: try to parse as-is and convert to user timezone
            try {
                return Carbon::parse($datetime, 'UTC')->setTimezone($userTimezone)->format('Y-m-d H:i:s');
            } catch (\Exception $e) {
                // Last resort: return as-is
                return $datetime;
            }
        }
    }

    /**
     * Get user timezone from request or session
     *
     * @return string
     */
    protected function getUserTimezone(): string
    {
        // Try to get from request first
        if (request()->has('user_timezone')) {
            return request()->input('user_timezone');
        }

        // Try to get from session
        if (session()->has('user_timezone')) {
            return session('user_timezone');
        }

        // Default to UTC
        return 'UTC';
    }
}
