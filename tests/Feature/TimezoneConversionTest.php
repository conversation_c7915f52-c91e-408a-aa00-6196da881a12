<?php

namespace Tests\Feature;

use App\Concerns\HandlesTimezoneConversion;
use Tests\TestCase;

class TimezoneConversionTest extends TestCase
{
    use HandlesTimezoneConversion;

    public function test_converts_datetime_to_utc()
    {
        // Test converting from EST to UTC
        $datetime = '2024-01-15 14:00:00'; // 2 PM EST
        $userTimezone = 'America/New_York';
        
        $utcDatetime = $this->convertToUtc($datetime, $userTimezone);
        
        // In January, EST is UTC-5, so 2 PM EST = 7 PM UTC
        $this->assertEquals('2024-01-15 19:00:00', $utcDatetime);
    }

    public function test_converts_datetime_from_utc()
    {
        // Test converting from UTC to EST
        $datetime = '2024-01-15 19:00:00'; // 7 PM UTC
        $userTimezone = 'America/New_York';
        
        $localDatetime = $this->convertFromUtc($datetime, $userTimezone);
        
        // 7 PM UTC = 2 PM EST in January
        $this->assertEquals('2024-01-15 14:00:00', $localDatetime);
    }

    public function test_handles_null_datetime()
    {
        $result = $this->convertToUtc(null, 'America/New_York');
        $this->assertNull($result);

        $result = $this->convertFromUtc(null, 'America/New_York');
        $this->assertNull($result);
    }

    public function test_defaults_to_utc_when_no_timezone_provided()
    {
        $datetime = '2024-01-15 14:00:00';
        
        $result = $this->convertToUtc($datetime, null);
        $this->assertEquals('2024-01-15 14:00:00', $result);
    }

    public function test_handles_different_timezones()
    {
        $datetime = '2024-01-15 14:00:00'; // 2 PM local time
        
        // Test with different timezones
        $timezones = [
            'America/New_York' => '2024-01-15 19:00:00', // UTC-5 in January
            'Europe/London' => '2024-01-15 14:00:00',    // UTC+0 in January
            'Asia/Tokyo' => '2024-01-15 05:00:00',       // UTC+9
            'Australia/Sydney' => '2024-01-15 03:00:00', // UTC+11 in January
        ];

        foreach ($timezones as $timezone => $expectedUtc) {
            $result = $this->convertToUtc($datetime, $timezone);
            $this->assertEquals($expectedUtc, $result, "Failed for timezone: {$timezone}");
        }
    }
}
